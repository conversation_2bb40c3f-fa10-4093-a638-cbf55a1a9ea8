// storage-adapter-import-placeholder
import { mongooseAdapter } from '@payloadcms/db-mongodb'
import { nodemailerAdapter } from '@payloadcms/email-nodemailer'

import sharp from 'sharp' // sharp-import
import path from 'path'
import { buildConfig, PayloadRequest, createLocalReq } from 'payload'
import { fileURLToPath } from 'url'

import { Categories } from './collections/Categories'
import { Media } from './collections/Media'
import { Pages } from './collections/Pages'
import { Posts } from './collections/Posts'
import { Users } from './collections/Users'
// New CMS Collections
import { Projects } from './collections/Projects'
import { SuccessStories } from './collections/SuccessStories'
import { Resources } from './collections/Resources'
import { News } from './collections/News'
import { MediaGallery } from './collections/MediaGallery'
import { Partnerships } from './collections/Partnerships'
import { InvestmentOpportunities } from './collections/InvestmentOpportunities'
import { Partners } from './collections/Partners'
import { PartnershipApplications } from './collections/PartnershipApplications'
import { ContactSubmissions } from './collections/ContactSubmissions'
import { Footer } from './Footer/config'
import { Header } from './Header/config'
import { plugins } from './plugins'
import { vercelBlobStorage } from '@payloadcms/storage-vercel-blob'
import { defaultLexical } from '@/fields/defaultLexical'
import { getServerSideURL } from './utilities/getURL'
import Events from './collections/Events'
import Speakers from './collections/Speakers'
import Counties from './collections/Counties'
import { eventsHandler } from './endpoints/events'
import {
  countiesHandler,
  countyByIdHandler,
  countiesInBoundsHandler,
  createCountyHandler,
  updateCountyHandler,
  deleteCountyHandler,
  countyUsersHandler,
} from './endpoints/counties'
// New CMS Endpoints
import { projectsHandler, projectByIdHandler, createProjectHandler, updateProjectHandler, deleteProjectHandler } from './endpoints/projects'
import { successStoriesHandler, successStoryByIdHandler } from './endpoints/success-stories'
import { resourcesHandler, resourceByIdHandler, downloadResourceHandler } from './endpoints/resources'
import { newsHandler, newsByIdHandler } from './endpoints/news'
import { mediaGalleryHandler, mediaItemByIdHandler } from './endpoints/media-gallery'
import { partnershipsHandler, partnershipByIdHandler } from './endpoints/partnerships'
import { partnersHandler, partnerByIdHandler } from './endpoints/partners'
import { investmentOpportunitiesHandler, investmentOpportunityByIdHandler } from './endpoints/investment-opportunities'
import {
  contactSubmissionsHandler,
  createContactSubmissionHandler,
  contactSubmissionByIdHandler,
  updateContactSubmissionHandler
} from './endpoints/contact-submissions'
import {
  createPartnershipApplicationHandler,
  partnershipApplicationsHandler,
  partnershipApplicationByIdHandler,
  updatePartnershipApplicationHandler,
} from './endpoints/partnership-applications'
import {
  createSuccessStoryHandler, updateSuccessStoryHandler, deleteSuccessStoryHandler,
  createResourceHandler, updateResourceHandler, deleteResourceHandler,
  createNewsHandler, updateNewsHandler, deleteNewsHandler,
  createMediaGalleryHandler, updateMediaGalleryHandler, deleteMediaGalleryHandler,
  createPartnershipHandler, updatePartnershipHandler, deletePartnershipHandler,
  createPartnerHandler, updatePartnerHandler, deletePartnerHandler,
  createInvestmentOpportunityHandler, updateInvestmentOpportunityHandler, deleteInvestmentOpportunityHandler,
  createEventHandler, updateEventHandler, deleteEventHandler,
  createPageHandler, updatePageHandler, deletePageHandler,
  createPostHandler, updatePostHandler, deletePostHandler,
  createCategoryHandler, updateCategoryHandler, deleteCategoryHandler,
  createSpeakerHandler, updateSpeakerHandler, deleteSpeakerHandler,
  createMediaHandler, updateMediaHandler, deleteMediaHandler,
} from './endpoints/content-crud'
import { seed } from './endpoints/seed'
import { seedCMSData } from './lib/seed/cms-data'

const filename = fileURLToPath(import.meta.url)
const dirname = path.dirname(filename)

export default buildConfig({
  onInit: async (payload) => {
    // Check if database is empty and seed if needed
    try {
      const users = await payload.find({
        collection: 'users',
        limit: 1,
        pagination: false,
      })

      if (users.docs.length === 0) {
        payload.logger.info('Database appears to be empty. Starting seeding process...')

        // Create a local request object for seeding
        const req = await createLocalReq({}, payload)

        await seed({ payload, req })

        // Seed CMS data after basic seeding
        await seedCMSData(payload)

        payload.logger.info('Database seeding completed successfully!')
      } else {
        payload.logger.info('Database already contains data. Skipping seeding.')
      }
    } catch (error) {
      payload.logger.error('Error during database seeding:', error)
    }
  },
  email: process.env.SMTP_HOST
    ? nodemailerAdapter({
        defaultFromAddress: process.env.FROM_EMAIL || '<EMAIL>',
        defaultFromName: process.env.FROM_NAME || 'Your App',
        transportOptions: {
          host: process.env.SMTP_HOST,
          port: parseInt(process.env.SMTP_PORT || '587', 10),
          secure: process.env.SMTP_SECURE === 'true', // true for 465, false for 587
          auth: {
            user: process.env.SMTP_USER,
            pass: process.env.SMTP_PASS,
          },
        },
      })
    : undefined,
  admin: {
    components: {
      // The `BeforeLogin` component renders a message that you see while logging into your admin panel.
      // Feel free to delete this at any time. Simply remove the line below.
      beforeLogin: ['@/components/BeforeLogin'],
      // The `BeforeDashboard` component renders the 'welcome' block that you see after logging into your admin panel.
      // Feel free to delete this at any time. Simply remove the line below.
      beforeDashboard: ['@/components/BeforeDashboard'],
    },
    importMap: {
      baseDir: path.resolve(dirname),
    },
    user: Users.slug,
    livePreview: {
      breakpoints: [
        {
          label: 'Mobile',
          name: 'mobile',
          width: 375,
          height: 667,
        },
        {
          label: 'Tablet',
          name: 'tablet',
          width: 768,
          height: 1024,
        },
        {
          label: 'Desktop',
          name: 'desktop',
          width: 1440,
          height: 900,
        },
      ],
    },
  },
  // This config helps us configure global or default features that the other editors can inherit
  editor: defaultLexical,
  db: mongooseAdapter({
    url: process.env.DATABASE_URI || '',
  }),
  collections: [
    // Original collections
    Pages,
    Posts,
    Media,
    Categories,
    Users,
    Events,
    Speakers,
    Counties,
    // New CMS collections
    Projects,
    SuccessStories,
    Resources,
    News,
    MediaGallery,
    Partnerships,
    InvestmentOpportunities,
    Partners,
    ContactSubmissions,
    PartnershipApplications,
  ],
  cors: [getServerSideURL()].filter(Boolean),
  globals: [Header, Footer],
  endpoints: [
    {
      path: '/events',
      method: 'get',
      handler: eventsHandler as any,
    },
    {
      path: '/events',
      method: 'post',
      handler: createEventHandler as any,
    },
    {
      path: '/events/:id',
      method: 'put',
      handler: updateEventHandler as any,
    },
    {
      path: '/events/:id',
      method: 'delete',
      handler: deleteEventHandler as any,
    },
    // Counties CRUD endpoints
    {
      path: '/counties',
      method: 'get',
      handler: countiesHandler as any,
    },
    {
      path: '/counties',
      method: 'post',
      handler: createCountyHandler as any,
    },
    {
      path: '/counties/:id',
      method: 'get',
      handler: countyByIdHandler as any,
    },
    {
      path: '/counties/:id',
      method: 'put',
      handler: updateCountyHandler as any,
    },
    {
      path: '/counties/:id',
      method: 'delete',
      handler: deleteCountyHandler as any,
    },
    {
      path: '/counties/bounds',
      method: 'get',
      handler: countiesInBoundsHandler as any,
    },
    {
      path: '/counties/:id/users',
      method: 'get',
      handler: countyUsersHandler as any,
    },
    // New CMS endpoints
    // Projects
    {
      path: '/projects',
      method: 'get',
      handler: projectsHandler as any,
    },
    {
      path: '/projects/:id',
      method: 'get',
      handler: projectByIdHandler as any,
    },
    {
      path: '/projects',
      method: 'post',
      handler: createProjectHandler as any,
    },
    {
      path: '/projects/:id',
      method: 'put',
      handler: updateProjectHandler as any,
    },
    {
      path: '/projects/:id',
      method: 'delete',
      handler: deleteProjectHandler as any,
    },
    // Success Stories
    {
      path: '/success-stories',
      method: 'get',
      handler: successStoriesHandler as any,
    },
    {
      path: '/success-stories/:id',
      method: 'get',
      handler: successStoryByIdHandler as any,
    },
    {
      path: '/success-stories',
      method: 'post',
      handler: createSuccessStoryHandler as any,
    },
    {
      path: '/success-stories/:id',
      method: 'put',
      handler: updateSuccessStoryHandler as any,
    },
    {
      path: '/success-stories/:id',
      method: 'delete',
      handler: deleteSuccessStoryHandler as any,
    },
    // Resources
    {
      path: '/resources',
      method: 'get',
      handler: resourcesHandler as any,
    },
    {
      path: '/resources/:id',
      method: 'get',
      handler: resourceByIdHandler as any,
    },
    {
      path: '/resources/:id/download',
      method: 'get',
      handler: downloadResourceHandler as any,
    },
    {
      path: '/resources',
      method: 'post',
      handler: createResourceHandler as any,
    },
    {
      path: '/resources/:id',
      method: 'put',
      handler: updateResourceHandler as any,
    },
    {
      path: '/resources/:id',
      method: 'delete',
      handler: deleteResourceHandler as any,
    },
    // News
    {
      path: '/news',
      method: 'get',
      handler: newsHandler as any,
    },
    {
      path: '/news/:id',
      method: 'get',
      handler: newsByIdHandler as any,
    },
    {
      path: '/news',
      method: 'post',
      handler: createNewsHandler as any,
    },
    {
      path: '/news/:id',
      method: 'put',
      handler: updateNewsHandler as any,
    },
    {
      path: '/news/:id',
      method: 'delete',
      handler: deleteNewsHandler as any,
    },
    // Media Gallery
    {
      path: '/media-gallery',
      method: 'get',
      handler: mediaGalleryHandler as any,
    },
    {
      path: '/media-gallery/:id',
      method: 'get',
      handler: mediaItemByIdHandler as any,
    },
    {
      path: '/media-gallery',
      method: 'post',
      handler: createMediaGalleryHandler as any,
    },
    {
      path: '/media-gallery/:id',
      method: 'put',
      handler: updateMediaGalleryHandler as any,
    },
    {
      path: '/media-gallery/:id',
      method: 'delete',
      handler: deleteMediaGalleryHandler as any,
    },
    // Partnerships
    {
      path: '/partnerships',
      method: 'get',
      handler: partnershipsHandler as any,
    },
    {
      path: '/partnerships/:id',
      method: 'get',
      handler: partnershipByIdHandler as any,
    },
    {
      path: '/partnerships',
      method: 'post',
      handler: createPartnershipHandler as any,
    },
    {
      path: '/partnerships/:id',
      method: 'put',
      handler: updatePartnershipHandler as any,
    },
    {
      path: '/partnerships/:id',
      method: 'delete',
      handler: deletePartnershipHandler as any,
    },
    // Partners
    {
      path: '/partners',
      method: 'get',
      handler: partnersHandler as any,
    },
    {
      path: '/partners/:id',
      method: 'get',
      handler: partnerByIdHandler as any,
    },
    {
      path: '/partners',
      method: 'post',
      handler: createPartnerHandler as any,
    },
    {
      path: '/partners/:id',
      method: 'put',
      handler: updatePartnerHandler as any,
    },
    {
      path: '/partners/:id',
      method: 'delete',
      handler: deletePartnerHandler as any,
    },
    // Investment Opportunities
    {
      path: '/investment-opportunities',
      method: 'get',
      handler: investmentOpportunitiesHandler as any,
    },
    {
      path: '/investment-opportunities/:id',
      method: 'get',
      handler: investmentOpportunityByIdHandler as any,
    },
    {
      path: '/investment-opportunities',
      method: 'post',
      handler: createInvestmentOpportunityHandler as any,
    },
    {
      path: '/investment-opportunities/:id',
      method: 'put',
      handler: updateInvestmentOpportunityHandler as any,
    },
    {
      path: '/investment-opportunities/:id',
      method: 'delete',
      handler: deleteInvestmentOpportunityHandler as any,
    },
    // Contact Submissions
    {
      path: '/contact-submissions',
      method: 'get',
      handler: contactSubmissionsHandler as any,
    },
    {
      path: '/contact-submissions',
      method: 'post',
      handler: createContactSubmissionHandler as any,
    },
    {
      path: '/contact-submissions/:id',
      method: 'get',
      handler: contactSubmissionByIdHandler as any,
    },
    {
      path: '/contact-submissions/:id',
      method: 'put',
      handler: updateContactSubmissionHandler as any,
    },
    // Partnership Applications
    {
      path: '/partnership-applications',
      method: 'get',
      handler: partnershipApplicationsHandler as any,
    },
    {
      path: '/partnership-applications',
      method: 'post',
      handler: createPartnershipApplicationHandler as any,
    },
    {
      path: '/partnership-applications/:id',
      method: 'get',
      handler: partnershipApplicationByIdHandler as any,
    },
    {
      path: '/partnership-applications/:id',
      method: 'put',
      handler: updatePartnershipApplicationHandler as any,
    },
    // Pages CRUD
    {
      path: '/pages',
      method: 'post',
      handler: createPageHandler as any,
    },
    {
      path: '/pages/:id',
      method: 'put',
      handler: updatePageHandler as any,
    },
    {
      path: '/pages/:id',
      method: 'delete',
      handler: deletePageHandler as any,
    },
    // Posts CRUD
    {
      path: '/posts',
      method: 'post',
      handler: createPostHandler as any,
    },
    {
      path: '/posts/:id',
      method: 'put',
      handler: updatePostHandler as any,
    },
    {
      path: '/posts/:id',
      method: 'delete',
      handler: deletePostHandler as any,
    },
    // Categories CRUD
    {
      path: '/categories',
      method: 'post',
      handler: createCategoryHandler as any,
    },
    {
      path: '/categories/:id',
      method: 'put',
      handler: updateCategoryHandler as any,
    },
    {
      path: '/categories/:id',
      method: 'delete',
      handler: deleteCategoryHandler as any,
    },
    // Speakers CRUD
    {
      path: '/speakers',
      method: 'post',
      handler: createSpeakerHandler as any,
    },
    {
      path: '/speakers/:id',
      method: 'put',
      handler: updateSpeakerHandler as any,
    },
    {
      path: '/speakers/:id',
      method: 'delete',
      handler: deleteSpeakerHandler as any,
    },
    // Media CRUD
    {
      path: '/media',
      method: 'post',
      handler: createMediaHandler as any,
    },
    {
      path: '/media/:id',
      method: 'put',
      handler: updateMediaHandler as any,
    },
    {
      path: '/media/:id',
      method: 'delete',
      handler: deleteMediaHandler as any,
    },
  ],
  plugins: [
    ...plugins,
    // Vercel Blob Storage for media uploads in production
    ...(process.env.BLOB_READ_WRITE_TOKEN
      ? [
          vercelBlobStorage({
            collections: {
              media: true,
              'media-gallery': {
                prefix: 'gallery',
              },
            },
            token: process.env.BLOB_READ_WRITE_TOKEN,
          }),
        ]
      : []),
  ],
  secret: process.env.PAYLOAD_SECRET,
  sharp,
  typescript: {
    outputFile: path.resolve(dirname, 'payload-types.ts'),
  },
  jobs: {
    access: {
      run: ({ req }: { req: PayloadRequest }): boolean => {
        // Allow logged in users to execute this endpoint (default)
        if (req.user) return true

        // If there is no logged in user, then check
        // for the Vercel Cron secret to be present as an
        // Authorization header:
        const authHeader = req.headers.get('authorization')
        return authHeader === `Bearer ${process.env.CRON_SECRET}`
      },
    },
    tasks: [],
  },
})
