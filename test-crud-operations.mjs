/**
 * CRUD Operations Testing Script
 * Tests Create, Read, Update, Delete operations for all collections
 */

const API_BASE_URL = 'http://localhost:3000'

// Helper function to make API calls
async function apiCall(endpoint, method = 'GET', data = null) {
  const url = `${API_BASE_URL}/api${endpoint}`
  const options = {
    method,
    headers: {
      'Content-Type': 'application/json',
    },
  }

  if (data) {
    options.body = JSON.stringify(data)
  }

  try {
    const response = await fetch(url, options)
    const result = await response.json()
    
    if (!response.ok) {
      throw new Error(`API Error: ${result.message || response.statusText}`)
    }
    
    return { success: true, data: result, status: response.status }
  } catch (error) {
    return { success: false, error: error.message }
  }
}

// Test CRUD operations for Projects
async function testProjectsCRUD() {
  console.log('\n🧪 Testing Projects CRUD Operations...')
  
  // Test CREATE
  console.log('\n📝 Testing CREATE operation...')
  const newProject = {
    title: 'CRUD Test Project',
    description: {
      root: {
        type: 'root',
        children: [{
          type: 'paragraph',
          children: [{
            type: 'text',
            text: 'This is a test project created via API to test CRUD operations.'
          }]
        }]
      }
    },
    summary: 'Test project for CRUD operations validation.',
    image: '6899eaf3ff648f1563ef9dc3', // Use existing media ID
    category: 'capacity-building',
    pillar: 'capacity-building',
    status: 'active',
    timeline: {
      startDate: '2024-01-01T00:00:00.000Z',
      endDate: '2024-12-31T00:00:00.000Z',
      duration: '12 months'
    },
    budget: {
      totalBudget: 1000000,
      currency: 'KES'
    },
    featured: false,
    published: true,
    tags: [
      { tag: 'testing' },
      { tag: 'crud' },
      { tag: 'api' }
    ]
  }
  
  const createResult = await apiCall('/projects', 'POST', newProject)
  if (createResult.success) {
    console.log('✅ CREATE: Successfully created project')
    const projectId = createResult.data.project?.id
    
    if (projectId) {
      // Test READ (specific project)
      console.log('\n📖 Testing READ operation (specific project)...')
      const readResult = await apiCall(`/projects/${projectId}`)
      if (readResult.success) {
        console.log('✅ READ: Successfully retrieved specific project')
      } else {
        console.log('❌ READ: Failed to retrieve specific project:', readResult.error)
      }
      
      // Test UPDATE
      console.log('\n✏️ Testing UPDATE operation...')
      const updateData = {
        id: projectId,
        title: 'CRUD Test Project - Updated',
        summary: 'Updated test project for CRUD operations validation.',
        status: 'completed'
      }

      const updateResult = await apiCall('/projects', 'PUT', updateData)
      if (updateResult.success) {
        console.log('✅ UPDATE: Successfully updated project')
      } else {
        console.log('❌ UPDATE: Failed to update project:', updateResult.error)
      }
      
      // Test DELETE
      console.log('\n🗑️ Testing DELETE operation...')
      const deleteResult = await apiCall(`/projects?id=${projectId}`, 'DELETE')
      if (deleteResult.success) {
        console.log('✅ DELETE: Successfully deleted project')
      } else {
        console.log('❌ DELETE: Failed to delete project:', deleteResult.error)
      }
    }
  } else {
    console.log('❌ CREATE: Failed to create project:', createResult.error)
  }
  
  // Test READ (list all projects)
  console.log('\n📖 Testing READ operation (list all projects)...')
  const listResult = await apiCall('/projects')
  if (listResult.success) {
    console.log(`✅ READ: Successfully retrieved ${listResult.data.projects?.length || 0} projects`)
  } else {
    console.log('❌ READ: Failed to retrieve projects list:', listResult.error)
  }
}

// Test CRUD operations for News
async function testNewsCRUD() {
  console.log('\n🧪 Testing News CRUD Operations...')
  
  // Test CREATE
  console.log('\n📝 Testing CREATE operation...')
  const newArticle = {
    title: 'CRUD Test News Article',
    excerpt: 'This is a test news article created via API to test CRUD operations.',
    summary: 'Test article for CRUD operations validation.',
    content: {
      root: {
        type: 'root',
        children: [{
          type: 'paragraph',
          children: [{
            type: 'text',
            text: 'This is a comprehensive test article created to validate CRUD operations for the news collection.'
          }]
        }]
      }
    },
    featuredImage: '6899eaf3ff648f1563ef9dc3', // Use existing media ID
    author: {
      name: 'Test Author',
      role: 'QA Engineer',
      organization: 'NPI Testing Team'
    },
    publishDate: '2024-01-15T10:00:00.000Z',
    category: 'news',
    type: 'article',
    status: 'published',
    priority: 'medium',
    featured: false,
    urgent: false,
    tags: [
      { tag: 'testing' },
      { tag: 'crud' },
      { tag: 'news' }
    ]
  }
  
  const createResult = await apiCall('/news', 'POST', newArticle)
  if (createResult.success) {
    console.log('✅ CREATE: Successfully created news article')
    const articleId = createResult.data.article?.id
    
    if (articleId) {
      // Test READ (specific article)
      console.log('\n📖 Testing READ operation (specific article)...')
      const readResult = await apiCall(`/news/${articleId}`)
      if (readResult.success) {
        console.log('✅ READ: Successfully retrieved specific article')
      } else {
        console.log('❌ READ: Failed to retrieve specific article:', readResult.error)
      }
      
      // Test UPDATE
      console.log('\n✏️ Testing UPDATE operation...')
      const updateData = {
        id: articleId,
        title: 'CRUD Test News Article - Updated',
        summary: 'Updated test article for CRUD operations validation.',
        priority: 'high'
      }

      const updateResult = await apiCall('/news', 'PUT', updateData)
      if (updateResult.success) {
        console.log('✅ UPDATE: Successfully updated article')
      } else {
        console.log('❌ UPDATE: Failed to update article:', updateResult.error)
      }
      
      // Test DELETE
      console.log('\n🗑️ Testing DELETE operation...')
      const deleteResult = await apiCall(`/news?id=${articleId}`, 'DELETE')
      if (deleteResult.success) {
        console.log('✅ DELETE: Successfully deleted article')
      } else {
        console.log('❌ DELETE: Failed to delete article:', deleteResult.error)
      }
    }
  } else {
    console.log('❌ CREATE: Failed to create news article:', createResult.error)
  }
  
  // Test READ (list all articles)
  console.log('\n📖 Testing READ operation (list all articles)...')
  const listResult = await apiCall('/news')
  if (listResult.success) {
    console.log(`✅ READ: Successfully retrieved ${listResult.data.news?.length || 0} articles`)
  } else {
    console.log('❌ READ: Failed to retrieve news list:', listResult.error)
  }
}

// Test CRUD operations for Success Stories
async function testSuccessStoriesCRUD() {
  console.log('\n🧪 Testing Success Stories CRUD Operations...')
  
  // Test READ (list all stories)
  console.log('\n📖 Testing READ operation (list all stories)...')
  const listResult = await apiCall('/success-stories')
  if (listResult.success) {
    console.log(`✅ READ: Successfully retrieved ${listResult.data.stories?.length || 0} success stories`)
  } else {
    console.log('❌ READ: Failed to retrieve success stories list:', listResult.error)
  }
}

// Main test function
async function runCRUDTests() {
  console.log('🚀 Starting CRUD Operations Testing...')
  
  try {
    await testProjectsCRUD()
    await testNewsCRUD()
    await testSuccessStoriesCRUD()
    
    console.log('\n🎉 CRUD Operations Testing Completed!')
    
  } catch (error) {
    console.error('💥 CRUD Testing failed:', error)
    process.exit(1)
  }
}

// Run the tests
runCRUDTests()
