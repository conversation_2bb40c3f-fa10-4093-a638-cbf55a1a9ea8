'use client'

import React, { useState } from 'react'
import { useProjects } from '@/lib/cms/hooks'
import {
  NPISection,
  NPISectionHeader,
  NPISectionTitle,
  NPISectionDescription,
} from '@/components/ui/npi-section'
import {
  NPICard,
} from '@/components/ui/npi-card'
import { NPIButton } from '@/components/ui/npi-button'
import { NPIOptimizedImage } from '@/components/ui/npi-optimized-image'
import { motion, AnimatePresence } from 'framer-motion'
import { X, MapPin, Calendar, Users, DollarSign, Target, Handshake } from 'lucide-react'

interface Project {
  id: string
  title: string
  description: string
  category: string
  pillar: string
  location: string
  duration: string
  status: string
  participants: number
  budget: string
  objectives: string[]
  partners: string[]
  image: string
  imageObject?: any
}

interface NPIProjectsListingProps {
  title?: string
  description?: string
  projects?: Project[]
}

const getStatusColor = (status: string) => {
  switch (status) {
    case 'active':
      return 'bg-[#25718A] text-white'
    case 'completed':
      return 'bg-[#8A3E25] text-white'
    case 'upcoming':
      return 'bg-[#725242] text-white'
    default:
      return 'bg-[#725242] text-white'
  }
}

const getCategoryColor = (category: string) => {
  const colors = [
    { bg: 'bg-[#8A3E25]', text: 'text-white', border: 'border-[#8A3E25]' },
    { bg: 'bg-[#25718A]', text: 'text-white', border: 'border-[#25718A]' },
    { bg: 'bg-[#725242]', text: 'text-white', border: 'border-[#725242]' },
    { bg: 'bg-[#EFE3BA]', text: 'text-black', border: 'border-[#8A3E25]' },
    { bg: 'bg-[#FFFFFF]', text: 'text-black', border: 'border-[#25718A]' },
  ]
  const index = category.length % colors.length
  return colors[index]
}

export const NPIProjectsListingBlock: React.FC<NPIProjectsListingProps> = ({
  title = 'Our Projects & Initiatives',
  description = "Comprehensive projects transforming Kenya's natural products landscape through community-driven innovation, capacity building, and sustainable development.",
  projects,
}) => {
  // React hooks must be called before any early returns
  const [selectedProject, setSelectedProject] = useState<Project | null>(null)
  const [filter, setFilter] = useState<string>('all')

  // Fetch projects from database
  const { data: projectsData, loading, error } = useProjects({ limit: 20 })

  // Default fallback projects (only used if no database data)
  const defaultProjects = [
    {
      id: 'knowledge-documentation',
      title: 'Indigenous Knowledge Documentation Project',
      description:
        "Systematic documentation of traditional knowledge systems across Kenya's 47 counties, creating a comprehensive digital repository for preservation and access.",
      category: 'Knowledge Preservation',
      pillar: 'Indigenous Knowledge Documentation',
      location: 'All 47 Counties',
      duration: '2022-2025',
      status: 'active',
      participants: 2500,
      budget: 'KES 150M',
      objectives: [
        'Document traditional medicinal practices',
        'Create digital knowledge repository',
        'Train community knowledge keepers',
        'Establish preservation protocols',
      ],
      partners: ['University of Nairobi', 'Kenya Medical Research Institute', 'County Governments'],
      image: '/assets/product 1.jpg',
      imageObject: null,
    }
  ]

  // Transform database projects to component format
  const transformProjectsData = (projectItems: any[]) => {
    return projectItems.map((item: any) => ({
      id: item.id,
      title: item.title,
      description: item.summary || (item.description?.root ? 'Project description available' : 'Description not available'),
      category: item.category || 'General',
      pillar: item.pillar || 'Development',
      location: item.location?.specificLocation || item.location?.counties?.[0]?.name || 'Kenya',
      duration: item.timeline?.duration || `${new Date(item.timeline?.startDate).getFullYear()}-${new Date(item.timeline?.endDate).getFullYear()}`,
      status: item.status || 'active',
      participants: Math.floor(Math.random() * 2000) + 500, // Placeholder since not in schema
      budget: item.budget ? `${item.budget.currency} ${(item.budget.totalBudget / 1000000).toFixed(1)}M` : 'Budget TBD',
      objectives: [
        'Achieve project goals',
        'Support community development',
        'Create sustainable impact',
        'Build local capacity',
      ], // Placeholder since not in schema
      partners: item.team?.implementingPartners || ['NPI', 'Community Partners'],
      image: item.image?.sizes?.card?.url || item.image?.url || '/assets/product 1.jpg',
      imageObject: item.image, // Pass full image object for optimization
    }))
  }

  // Use database data if available, otherwise use provided projects or fallback
  const displayProjects = projectsData && projectsData.length > 0
    ? transformProjectsData(projectsData)
    : projects || defaultProjects

  // Show loading state
  if (loading) {
    return (
      <NPISection className="py-24 bg-[#FFFFFF]">
        <div className="container mx-auto px-4">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto"></div>
            <p className="mt-4 text-gray-600">Loading projects...</p>
          </div>
        </div>
      </NPISection>
    )
  }

  // Show error state
  if (error) {
    console.error('Error loading projects:', error)
  }

  const categories = ['all', ...Array.from(new Set(displayProjects.map((p) => p.category)))]
  const filteredProjects =
    filter === 'all' ? displayProjects : displayProjects.filter((p) => p.category === filter)

  return (
    <NPISection className="py-24 bg-[#FFFFFF]">
      <div className="container mx-auto px-4">
        <NPISectionHeader className="text-center mb-16">
          <NPISectionTitle className="text-black mb-4">{title}</NPISectionTitle>
          <NPISectionDescription className="text-[#725242] max-w-3xl mx-auto">
            {description}
          </NPISectionDescription>
        </NPISectionHeader>

        {/* Filter Buttons */}
        <div className="flex flex-wrap justify-center gap-4 mb-12">
          {categories.map((category) => (
            <NPIButton
              key={category}
              variant={filter === category ? 'primary' : 'outline'}
              onClick={() => setFilter(category)}
              className={`${
                filter === category
                  ? 'bg-[#8A3E25] text-white border-[#8A3E25]'
                  : 'border-[#25718A] text-[#25718A] hover:bg-[#25718A] hover:text-white'
              } transition-all duration-300`}
            >
              {category === 'all' ? 'All Projects' : category}
            </NPIButton>
          ))}
        </div>

        {/* Projects Grid - Square cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {filteredProjects.map((project, index) => (
            <motion.div
              key={project.id}
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              whileHover={{ scale: 1.05, y: -10 }}
              className="aspect-square"
            >
              <NPICard className="h-full w-full overflow-hidden hover:shadow-2xl transition-all duration-300 border-2 border-transparent hover:border-[#725242]/30 flex flex-col">
                {/* Square Image Section - Takes up top 45% */}
                <div className="relative h-[45%] w-full flex-shrink-0">
                  <NPIOptimizedImage
                    image={project.imageObject || project.image}
                    alt={project.title}
                    fill
                    className="object-cover"
                    preferredSize="card"
                  />
                  <div className="absolute top-2 left-2">
                    <span
                      className={`px-2 py-1 text-xs font-medium ${getStatusColor(project.status)}`}
                    >
                      {project.status.charAt(0).toUpperCase() + project.status.slice(1)}
                    </span>
                  </div>
                  <div className="absolute top-2 right-2">
                    <span
                      className={`px-2 py-1 text-xs font-bold ${getCategoryColor(project.category).bg} ${getCategoryColor(project.category).text} border ${getCategoryColor(project.category).border}`}
                    >
                      {project.category}
                    </span>
                  </div>
                </div>

                {/* Content Section - Takes up remaining 55% */}
                <div className="h-[55%] p-3 flex flex-col justify-between">
                  {/* Title - More space */}
                  <div className="flex-1 flex flex-col justify-center">
                    <h3 className="text-black text-sm font-bold mb-3 line-clamp-3 leading-tight text-center">
                      {project.title}
                    </h3>
                  </div>

                  {/* Essential Details Only */}
                  <div className="space-y-2 mb-3">
                    <div className="flex items-center justify-center gap-2 text-xs text-[#725242]">
                      <MapPin className="w-3 h-3 text-[#8A3E25] flex-shrink-0" />
                      <span className="font-medium">{project.location}</span>
                    </div>
                    <div className="text-center">
                      <span className="text-xs text-[#725242] font-medium">{project.duration}</span>
                    </div>
                  </div>

                  {/* Button - Smaller */}
                  <NPIButton
                    onClick={() => setSelectedProject(project)}
                    className="w-full bg-[#8A3E25] hover:bg-[#25718A] text-white font-medium transition-all duration-300 border-2 border-[#8A3E25] hover:border-[#25718A] text-xs py-1.5"
                  >
                    View Details
                  </NPIButton>
                </div>
              </NPICard>
            </motion.div>
          ))}
        </div>
      </div>

      {/* Project Detail Modal */}
      <AnimatePresence>
        {selectedProject && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50"
            onClick={() => setSelectedProject(null)}
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              className="bg-white max-w-4xl w-full max-h-[90vh] overflow-y-auto relative"
              onClick={(e) => e.stopPropagation()}
            >
              {/* Hero Section */}
              <div className="relative h-64 w-full">
                <NPIOptimizedImage
                  image={selectedProject.imageObject || selectedProject.image}
                  alt={selectedProject.title}
                  fill
                  className="object-cover"
                  preferredSize="hero"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent" />
                <NPIButton
                  onClick={() => setSelectedProject(null)}
                  className="absolute top-4 right-4 bg-[#8A3E25] hover:bg-[#25718A] text-white p-2 transition-colors border border-white/20"
                >
                  <X className="w-6 h-6" />
                </NPIButton>
                <div className="absolute bottom-6 left-6 text-white">
                  <h2 className="text-2xl md:text-3xl font-bold mb-2">{selectedProject.title}</h2>
                  <div className="flex items-center gap-4">
                    <span
                      className={`px-3 py-1 text-sm font-medium ${getStatusColor(selectedProject.status)}`}
                    >
                      {selectedProject.status.charAt(0).toUpperCase() +
                        selectedProject.status.slice(1)}
                    </span>
                    <span
                      className={`px-3 py-1 text-sm font-bold ${getCategoryColor(selectedProject.category).bg} ${getCategoryColor(selectedProject.category).text} border ${getCategoryColor(selectedProject.category).border}`}
                    >
                      {selectedProject.category}
                    </span>
                  </div>
                </div>
              </div>

              {/* Content */}
              <div className="p-8">
                <div className="grid md:grid-cols-2 gap-8">
                  {/* Left Column */}
                  <div className="space-y-6">
                    <div>
                      <h3 className="text-xl font-bold text-black mb-3">Project Overview</h3>
                      <p className="text-[#725242] leading-relaxed">{selectedProject.description}</p>
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div className="bg-[#E5E1DC]/50 p-4">
                        <div className="flex items-center gap-2 mb-2">
                          <MapPin className="w-4 h-4 text-[#8A3E25]" />
                          <span className="text-sm font-medium text-[#725242]">Location</span>
                        </div>
                        <p className="text-black font-bold">{selectedProject.location}</p>
                      </div>
                      <div className="bg-[#E5E1DC]/50 p-4">
                        <div className="flex items-center gap-2 mb-2">
                          <Calendar className="w-4 h-4 text-[#8A3E25]" />
                          <span className="text-sm font-medium text-[#725242]">Duration</span>
                        </div>
                        <p className="text-black font-bold">{selectedProject.duration}</p>
                      </div>
                      <div className="bg-[#E5E1DC]/50 p-4">
                        <div className="flex items-center gap-2 mb-2">
                          <Users className="w-4 h-4 text-[#8A3E25]" />
                          <span className="text-sm font-medium text-[#725242]">Participants</span>
                        </div>
                        <p className="text-black font-bold">{selectedProject.participants.toLocaleString()}</p>
                      </div>
                      <div className="bg-[#E5E1DC]/50 p-4">
                        <div className="flex items-center gap-2 mb-2">
                          <DollarSign className="w-4 h-4 text-[#8A3E25]" />
                          <span className="text-sm font-medium text-[#725242]">Budget</span>
                        </div>
                        <p className="text-black font-bold">{selectedProject.budget}</p>
                      </div>
                    </div>
                  </div>

                  {/* Right Column */}
                  <div className="space-y-6">
                    <div>
                      <h3 className="text-xl font-bold text-black mb-3 flex items-center gap-2">
                        <Target className="w-5 h-5 text-[#8A3E25]" />
                        Key Objectives
                      </h3>
                      <ul className="space-y-2">
                        {selectedProject.objectives.map((objective, index) => (
                          <li key={index} className="flex items-start gap-2">
                            <div className="w-2 h-2 bg-[#8A3E25] mt-2 flex-shrink-0" />
                            <span className="text-[#725242]">{objective}</span>
                          </li>
                        ))}
                      </ul>
                    </div>

                    <div>
                      <h3 className="text-xl font-bold text-black mb-3 flex items-center gap-2">
                        <Handshake className="w-5 h-5 text-[#8A3E25]" />
                        Key Partners
                      </h3>
                      <div className="flex flex-wrap gap-2">
                        {selectedProject.partners.map((partner, index) => (
                          <span
                            key={index}
                            className="px-3 py-1 bg-gradient-to-r from-[#725242] to-[#8A3E25] text-[#FFFFFF] text-sm font-medium"
                          >
                            {partner}
                          </span>
                        ))}
                      </div>
                    </div>

                    <div className="pt-4">
                      <NPIButton
                        className="w-full bg-gradient-to-r from-[#8A3E25] to-[#725242] hover:from-[#725242] hover:to-[#8A3E25] text-[#FFFFFF] font-bold py-3"
                        onClick={() => setSelectedProject(null)}
                      >
                        Close Details
                      </NPIButton>
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </NPISection>
  )
}