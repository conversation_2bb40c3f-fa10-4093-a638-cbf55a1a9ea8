import type { CollectionConfig } from 'payload'
import { authenticated } from '../../access/authenticated'
import { anyone } from '../../access/anyone'
import { slugField } from '@/fields/slug'

export const Resources: CollectionConfig = {
  slug: 'resources',
  access: {
    create: authenticated,
    delete: authenticated,
    read: anyone,
    update: authenticated,
  },
  admin: {
    useAsTitle: 'title',
    defaultColumns: ['title', 'type', 'category', 'publishDate', 'downloadCount'],
    group: 'Content Management',
  },
  labels: {
    singular: 'Resource',
    plural: 'Resources & Publications',
  },
  fields: [
    {
      name: 'title',
      type: 'text',
      required: true,
      admin: {
        description: 'The resource title',
      },
    },
    {
      name: 'description',
      type: 'richText',
      required: true,
      admin: {
        description: 'Detailed resource description',
      },
    },
    {
      name: 'summary',
      type: 'textarea',
      required: true,
      maxLength: 300,
      admin: {
        description: 'Brief resource summary for cards and previews (max 300 characters)',
      },
    },
    {
      name: 'type',
      type: 'select',
      required: true,
      options: [
        { label: 'Research Report', value: 'research-report' },
        { label: 'Policy Document', value: 'policy-document' },
        { label: 'Training Guide', value: 'training-guide' },
        { label: 'Best Practices', value: 'best-practices' },
        { label: 'Case Study', value: 'case-study' },
        { label: 'Technical Manual', value: 'technical-manual' },
        { label: 'Presentation', value: 'presentation' },
        { label: 'Video', value: 'video' },
        { label: 'Infographic', value: 'infographic' },
        { label: 'Toolkit', value: 'toolkit' },
        { label: 'White Paper', value: 'white-paper' },
        { label: 'Newsletter', value: 'newsletter' },
      ],
    },
    {
      name: 'category',
      type: 'select',
      required: true,
      options: [
        { label: 'Indigenous Knowledge', value: 'indigenous-knowledge' },
        { label: 'Community Development', value: 'community-development' },
        { label: 'Capacity Building', value: 'capacity-building' },
        { label: 'Market Development', value: 'market-development' },
        { label: 'Policy & Governance', value: 'policy-governance' },
        { label: 'Research & Innovation', value: 'research-innovation' },
        { label: 'Sustainability', value: 'sustainability' },
        { label: 'Technology', value: 'technology' },
        { label: 'Finance & Investment', value: 'finance-investment' },
      ],
    },
    {
      name: 'file',
      type: 'upload',
      relationTo: 'media',
      required: true,
      admin: {
        description: 'The main resource file (PDF, video, etc.)',
      },
    },
    {
      name: 'coverImage',
      type: 'upload',
      relationTo: 'media',
      admin: {
        description: 'Cover image or thumbnail for the resource',
      },
    },
    {
      name: 'additionalFiles',
      type: 'array',
      dbName: 'add_files',
      fields: [
        {
          name: 'title',
          type: 'text',
          required: true,
        },
        {
          name: 'file',
          type: 'upload',
          relationTo: 'media',
          required: true,
        },
        {
          name: 'description',
          type: 'textarea',
        },
      ],
      admin: {
        description: 'Additional supporting files',
      },
    },
    {
      name: 'metadata',
      type: 'group',
      fields: [
        {
          name: 'authors',
          type: 'array',
          fields: [
            {
              name: 'name',
              type: 'text',
              required: true,
            },
            {
              name: 'organization',
              type: 'text',
            },
            {
              name: 'role',
              type: 'text',
            },
          ],
        },
        {
          name: 'publishDate',
          type: 'date',
          required: true,
        },
        {
          name: 'lastUpdated',
          type: 'date',
        },
        {
          name: 'version',
          type: 'text',
          defaultValue: '1.0',
        },
        {
          name: 'language',
          type: 'select',
          defaultValue: 'en',
          options: [
            { label: 'English', value: 'en' },
            { label: 'Swahili', value: 'sw' },
            { label: 'Kikuyu', value: 'ki' },
            { label: 'Luo', value: 'luo' },
            { label: 'Kalenjin', value: 'kln' },
          ],
        },
        {
          name: 'pageCount',
          type: 'number',
          admin: {
            description: 'Number of pages (for documents)',
          },
        },
        {
          name: 'fileSize',
          type: 'text',
          admin: {
            description: 'File size (e.g., "2.5 MB")',
          },
        },
        {
          name: 'isbn',
          type: 'text',
          admin: {
            description: 'ISBN for published books/reports',
          },
        },
        {
          name: 'doi',
          type: 'text',
          admin: {
            description: 'Digital Object Identifier for academic publications',
          },
        },
      ],
    },
    {
      name: 'access',
      type: 'group',
      fields: [
        {
          name: 'level',
          type: 'select',
          required: true,
          defaultValue: 'public',
          options: [
            { label: 'Public', value: 'public' },
            { label: 'Registered Users', value: 'registered' },
            { label: 'Partners Only', value: 'partners' },
            { label: 'Internal', value: 'internal' },
          ],
        },
        {
          name: 'requiresRegistration',
          type: 'checkbox',
          dbName: 'req_registration',
          defaultValue: false,
        },
        {
          name: 'downloadLimit',
          type: 'number',
          admin: {
            description: 'Maximum downloads per user (0 = unlimited)',
          },
        },
      ],
    },
    {
      name: 'analytics',
      type: 'group',
      fields: [
        {
          name: 'downloadCount',
          type: 'number',
          defaultValue: 0,
          admin: {
            readOnly: true,
            description: 'Total number of downloads',
          },
        },
        {
          name: 'viewCount',
          type: 'number',
          defaultValue: 0,
          admin: {
            readOnly: true,
            description: 'Total number of views',
          },
        },
        {
          name: 'lastDownloaded',
          type: 'date',
          admin: {
            readOnly: true,
          },
        },
      ],
    },
    {
      name: 'relatedResources',
      type: 'relationship',
      dbName: 'related_res',
      relationTo: 'resources',
      hasMany: true,
      admin: {
        description: 'Related resources and publications',
      },
    },
    {
      name: 'relatedProjects',
      type: 'relationship',
      dbName: 'related_proj',
      relationTo: 'projects',
      hasMany: true,
      admin: {
        description: 'Related projects',
      },
    },
    {
      name: 'keywords',
      type: 'array',
      fields: [
        {
          name: 'keyword',
          type: 'text',
        },
      ],
      admin: {
        description: 'Keywords for search and categorization',
      },
    },
    {
      name: 'featured',
      type: 'checkbox',
      defaultValue: false,
      admin: {
        description: 'Feature this resource on homepage and key sections',
      },
    },
    {
      name: 'published',
      type: 'checkbox',
      defaultValue: true,
      admin: {
        description: 'Make this resource visible to the public',
      },
    },
    {
      name: 'externalUrl',
      type: 'text',
      admin: {
        description: 'External URL if resource is hosted elsewhere',
      },
    },
    ...slugField(),
  ],
}

export default Resources
