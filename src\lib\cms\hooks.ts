'use client'

import { useState, useEffect, useCallback } from 'react'
import { cmsAPI, CMSApiError } from './api'
import type {
  CMSProject,
  CMSSuccessStory,
  CMSResource,
  CMSNews,
  CMSMediaItem,
  CMSInvestmentOpportunity,
  CMSQueryParams,
  CMSListResponse,
  CMSResponse,
} from './types'

interface UseAsyncState<T> {
  data: T | null
  loading: boolean
  error: string | null
  refetch: () => Promise<void>
}

interface UseAsyncListState<T> {
  data: T[]
  loading: boolean
  error: string | null
  totalDocs: number
  page: number
  limit: number
  totalPages: number
  hasNextPage: boolean
  hasPrevPage: boolean
  refetch: () => Promise<void>
  loadMore: () => Promise<void>
  canLoadMore: boolean
}

// Generic hook for single item fetching
function useAsyncData<T>(
  fetchFn: () => Promise<CMSResponse<T>>,
  deps: any[] = [],
): UseAsyncState<T> {
  const [data, setData] = useState<T | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const fetchData = useCallback(async () => {
    try {
      setLoading(true)
      setError(null)
      const response = await fetchFn()
      setData(response.data)
    } catch (err) {
      const errorMessage = err instanceof CMSApiError ? err.message : 'An error occurred'
      setError(errorMessage)
      console.error('CMS API Error:', err)
    } finally {
      setLoading(false)
    }
  }, deps)

  useEffect(() => {
    fetchData()
  }, [fetchData])

  return {
    data,
    loading,
    error,
    refetch: fetchData,
  }
}

// Generic hook for list fetching with pagination
function useAsyncListData<T>(
  fetchFn: (params: CMSQueryParams) => Promise<CMSListResponse<T>>,
  initialParams: CMSQueryParams = {},
  deps: any[] = [],
): UseAsyncListState<T> {
  const [data, setData] = useState<T[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [totalDocs, setTotalDocs] = useState(0)
  const [page, setPage] = useState(initialParams.page || 1)
  const [limit] = useState(initialParams.limit || 20)
  const [totalPages, setTotalPages] = useState(0)
  const [hasNextPage, setHasNextPage] = useState(false)
  const [hasPrevPage, setHasPrevPage] = useState(false)
  const [params, setParams] = useState(initialParams)

  const fetchData = useCallback(async (resetData = true) => {
    try {
      setLoading(true)
      setError(null)
      const response = await fetchFn({ ...params, page, limit })
      
      if (resetData) {
        setData(response.data)
      } else {
        setData(prev => [...prev, ...response.data])
      }
      
      setTotalDocs(response.totalDocs)
      setTotalPages(response.totalPages)
      setHasNextPage(response.hasNextPage)
      setHasPrevPage(response.hasPrevPage)
    } catch (err) {
      const errorMessage = err instanceof CMSApiError ? err.message : 'An error occurred'
      setError(errorMessage)
      console.error('CMS API Error:', err)
    } finally {
      setLoading(false)
    }
  }, [fetchFn, params, page, limit, ...deps])

  const loadMore = useCallback(async () => {
    if (hasNextPage && !loading) {
      setPage(prev => prev + 1)
      await fetchData(false)
    }
  }, [hasNextPage, loading, fetchData])

  useEffect(() => {
    fetchData(true)
  }, [fetchData])

  return {
    data,
    loading,
    error,
    totalDocs,
    page,
    limit,
    totalPages,
    hasNextPage,
    hasPrevPage,
    refetch: () => fetchData(true),
    loadMore,
    canLoadMore: hasNextPage && !loading,
  }
}

// Projects Hooks
export function useProjects(params: CMSQueryParams = {}) {
  return useAsyncListData(cmsAPI.projects.getAll, params, [JSON.stringify(params)])
}

export function useProject(id: string) {
  return useAsyncData(() => cmsAPI.projects.getById(id), [id])
}

export function useFeaturedProjects(limit = 6) {
  return useAsyncListData(cmsAPI.projects.getFeatured, { limit }, [limit])
}

export function useProjectsByCategory(category: string, params: CMSQueryParams = {}) {
  return useAsyncListData(
    (p) => cmsAPI.projects.getByCategory(category, p),
    params,
    [category, JSON.stringify(params)]
  )
}

// Success Stories Hooks
export function useSuccessStories(params: CMSQueryParams = {}) {
  return useAsyncListData(cmsAPI.successStories.getAll, params, [JSON.stringify(params)])
}

export function useSuccessStory(id: string) {
  return useAsyncData(() => cmsAPI.successStories.getById(id), [id])
}

export function useFeaturedSuccessStories(limit = 6) {
  return useAsyncListData(cmsAPI.successStories.getFeatured, { limit }, [limit])
}

export function useSuccessStoriesByCategory(category: string, params: CMSQueryParams = {}) {
  return useAsyncListData(
    (p) => cmsAPI.successStories.getByCategory(category, p),
    params,
    [category, JSON.stringify(params)]
  )
}

// Resources Hooks
export function useResources(params: CMSQueryParams = {}) {
  return useAsyncListData(cmsAPI.resources.getAll, params, [JSON.stringify(params)])
}

export function useResource(id: string) {
  return useAsyncData(() => cmsAPI.resources.getById(id), [id])
}

export function useFeaturedResources(limit = 6) {
  return useAsyncListData(cmsAPI.resources.getFeatured, { limit }, [limit])
}

export function useResourcesByType(type: string, params: CMSQueryParams = {}) {
  return useAsyncListData(
    (p) => cmsAPI.resources.getByType(type, p),
    params,
    [type, JSON.stringify(params)]
  )
}

export function useResourcesByCategory(category: string, params: CMSQueryParams = {}) {
  return useAsyncListData(
    (p) => cmsAPI.resources.getByCategory(category, p),
    params,
    [category, JSON.stringify(params)]
  )
}

// News Hooks
export function useNews(params: CMSQueryParams = {}) {
  return useAsyncListData(cmsAPI.news.getAll, params, [JSON.stringify(params)])
}

export function useNewsArticle(id: string) {
  return useAsyncData(() => cmsAPI.news.getById(id), [id])
}

export function useFeaturedNews(limit = 6) {
  return useAsyncListData(cmsAPI.news.getFeatured, { limit }, [limit])
}

export function useUrgentNews(limit = 3) {
  return useAsyncListData(cmsAPI.news.getUrgent, { limit }, [limit])
}

export function useNewsByCategory(category: string, params: CMSQueryParams = {}) {
  return useAsyncListData(
    (p) => cmsAPI.news.getByCategory(category, p),
    params,
    [category, JSON.stringify(params)]
  )
}

// Media Gallery Hooks
export function useMediaGallery(params: CMSQueryParams = {}) {
  return useAsyncListData(cmsAPI.mediaGallery.getAll, params, [JSON.stringify(params)])
}

export function useMediaItem(id: string) {
  return useAsyncData(() => cmsAPI.mediaGallery.getById(id), [id])
}

export function useFeaturedMedia(limit = 12) {
  return useAsyncListData(cmsAPI.mediaGallery.getFeatured, { limit }, [limit])
}

export function useMediaByType(type: string, params: CMSQueryParams = {}) {
  return useAsyncListData(
    (p) => cmsAPI.mediaGallery.getByType(type, p),
    params,
    [type, JSON.stringify(params)]
  )
}

export function useMediaByCategory(category: string, params: CMSQueryParams = {}) {
  return useAsyncListData(
    (p) => cmsAPI.mediaGallery.getByCategory(category, p),
    params,
    [category, JSON.stringify(params)]
  )
}

// Investment Opportunities Hooks
export function useInvestmentOpportunities(params: CMSQueryParams = {}) {
  return useAsyncListData(cmsAPI.investmentOpportunities.getAll, params, [JSON.stringify(params)])
}

export function useInvestmentOpportunity(id: string) {
  return useAsyncData(() => cmsAPI.investmentOpportunities.getById(id), [id])
}

export function useFeaturedInvestmentOpportunities(limit = 6) {
  return useAsyncListData(cmsAPI.investmentOpportunities.getFeatured, { limit }, [limit])
}

export function useUrgentInvestmentOpportunities(limit = 3) {
  return useAsyncListData(cmsAPI.investmentOpportunities.getUrgent, { limit }, [limit])
}

export function useInvestmentOpportunitiesBySector(sector: string, params: CMSQueryParams = {}) {
  return useAsyncListData(
    (p) => cmsAPI.investmentOpportunities.getBySector(sector, p),
    params,
    [sector, JSON.stringify(params)]
  )
}

export function useInvestmentOpportunitiesByType(investmentType: string, params: CMSQueryParams = {}) {
  return useAsyncListData(
    (p) => cmsAPI.investmentOpportunities.getByInvestmentType(investmentType, p),
    params,
    [investmentType, JSON.stringify(params)]
  )
}

// Contact Form Hook
export function useContactForm() {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState(false)

  const submitForm = useCallback(async (data: {
    name: string
    email: string
    phone?: string
    organization?: string
    role?: string
    subject: string
    category: string
    message: string
    location?: {
      county?: string
      city?: string
      country?: string
    }
    priority?: string
  }) => {
    try {
      setLoading(true)
      setError(null)
      setSuccess(false)
      
      await cmsAPI.contact.submit(data)
      setSuccess(true)
    } catch (err) {
      const errorMessage = err instanceof CMSApiError ? err.message : 'Failed to submit form'
      setError(errorMessage)
      console.error('Contact form error:', err)
    } finally {
      setLoading(false)
    }
  }, [])

  const reset = useCallback(() => {
    setError(null)
    setSuccess(false)
  }, [])

  return {
    submitForm,
    loading,
    error,
    success,
    reset,
  }
}

// Hook for partnership application form
export const usePartnershipApplicationForm = () => {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState(false)

  const submitApplication = useCallback(async (data: {
    organizationName: string
    organizationType: string
    website?: string
    establishedYear?: string
    contactName: string
    contactTitle?: string
    email: string
    phone?: string
    partnershipModel: string
    investmentCapacity?: string
    projectInterest?: string
    timeline?: string
    experience?: string
    objectives?: string
    additionalInfo?: string
    priority?: string
  }) => {
    try {
      setLoading(true)
      setError(null)
      setSuccess(false)

      await cmsAPI.partnershipApplicationAPI.submit(data)
      setSuccess(true)
    } catch (err) {
      const errorMessage = err instanceof CMSApiError ? err.message : 'Failed to submit partnership application'
      setError(errorMessage)
      console.error('Partnership application error:', err)
    } finally {
      setLoading(false)
    }
  }, [])

  const reset = useCallback(() => {
    setError(null)
    setSuccess(false)
  }, [])

  return {
    submitApplication,
    loading,
    error,
    success,
    reset,
  }
}
